/* eslint-disable prettier/prettier */
// This file is auto-generated by the schema-generator.js script.
// Do not edit this file directly.
'use strict';
const { DataTypes, Sequelize } = require('sequelize');

module.exports = {
  "allSchemas": {
    "internal_job_profile_data": {
      "id": {
        "type": DataTypes.INTEGER,
        "primaryKey": true,
        "allowNull": false,
        "autoIncrement": true
      },
      "job_division": {
        "type": DataTypes.STRING
      },
      "job_group": {
        "type": DataTypes.STRING
      },
      "position_name": {
        "type": DataTypes.STRING
      },
      "job_classification": {
        "type": DataTypes.STRING
      },
      "job_family": {
        "type": DataTypes.STRING
      },
      "sub_job_family": {
        "type": DataTypes.STRING
      },
      "main_responsibilities": {
        "type": DataTypes.TEXT
      },
      "work_input": {
        "type": DataTypes.TEXT
      },
      "work_output": {
        "type": DataTypes.TEXT
      },
      "success_criteria": {
        "type": DataTypes.TEXT
      },
      "requirement": {
        "type": DataTypes.TEXT
      },
      "competency": {
        "type": DataTypes.TEXT
      },
      "created_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "updated_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      }
    },
    "job_group_variables": {
      "id": {
        "type": DataTypes.INTEGER,
        "primaryKey": true,
        "allowNull": false,
        "autoIncrement": true
      },
      "name": {
        "type": DataTypes.STRING,
        "allowNull": false
      },
      "keywords": {
        "type": DataTypes.ARRAY(DataTypes.STRING),
        "allowNull": false,
        "defaultValue": []
      },
      "created_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "updated_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "description": {
        "type": DataTypes.TEXT
      },
      "order_level": {
        "type": DataTypes.INTEGER,
        "allowNull": false,
        "defaultValue": 0
      }
    },
    "job_levels": {
      "id": {
        "type": DataTypes.INTEGER,
        "primaryKey": true,
        "allowNull": false,
        "autoIncrement": true
      },
      "name": {
        "type": DataTypes.STRING,
        "allowNull": false
      },
      "order_level": {
        "type": DataTypes.INTEGER,
        "allowNull": false,
        "defaultValue": 0
      },
      "created_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "updated_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      }
    },
    "job_titles": {
      "id": {
        "type": DataTypes.INTEGER,
        "primaryKey": true,
        "allowNull": false,
        "autoIncrement": true
      },
      "name": {
        "type": DataTypes.STRING,
        "allowNull": false,
        "unique": true
      },
      "prefilled_details": {
        "type": DataTypes.JSONB
      },
      "created_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "updated_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      }
    },
    "job_vacancies": {
      "id": {
        "type": DataTypes.INTEGER,
        "primaryKey": true,
        "allowNull": false,
        "autoIncrement": true
      },
      "name": {
        "type": DataTypes.STRING,
        "allowNull": false
      },
      "department": {
        "type": DataTypes.STRING
      },
      "job_grade": {
        "type": DataTypes.STRING
      },
      "job_description": {
        "type": DataTypes.TEXT
      },
      "job_title_id": {
        "type": DataTypes.INTEGER
      },
      "created_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "updated_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "job_desc": {
        "type": DataTypes.ARRAY(DataTypes.STRING),
        "allowNull": false,
        "defaultValue": []
      },
      "ksao": {
        "type": DataTypes.JSONB,
        "allowNull": false,
        "defaultValue": {
          "skills": [],
          "abilities": [],
          "knowledges": [],
          "other_characteristics": []
        }
      },
      "related_user_ids": {
        "type": DataTypes.ARRAY(DataTypes.INTEGER)
      },
      "related_onetsoc_codes": {
        "type": DataTypes.ARRAY(DataTypes.STRING)
      },
      "status": {
        "type": DataTypes.STRING,
        "allowNull": false,
        "defaultValue": "draft"
      },
      "detailed_descriptions": {
        "type": DataTypes.JSON,
        "defaultValue": {}
      },
      "job_level_id": {
        "type": DataTypes.INTEGER
      }
    },
    "job_variables": {
      "id": {
        "type": DataTypes.INTEGER,
        "primaryKey": true,
        "allowNull": false,
        "autoIncrement": true
      },
      "job_group_variable_id": {
        "type": DataTypes.INTEGER,
        "allowNull": false
      },
      "created_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "updated_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "name": {
        "type": DataTypes.STRING
      },
      "normalized_baseline": {
        "type": DataTypes.DOUBLE
      },
      "variable_type": {
        "type": DataTypes.STRING,
        "allowNull": false
      }
    },
    "llm_metadata": {
      "id": {
        "type": DataTypes.INTEGER,
        "primaryKey": true,
        "allowNull": false,
        "autoIncrement": true
      },
      "request": {
        "type": DataTypes.JSON
      },
      "responses": {
        "type": DataTypes.JSON
      },
      "created_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "updated_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "action_type": {
        "type": DataTypes.STRING
      }
    },
    "user_assessment_results": {
      "id": {
        "type": DataTypes.INTEGER,
        "primaryKey": true,
        "allowNull": false,
        "autoIncrement": true
      },
      "user_id": {
        "type": DataTypes.INTEGER
      },
      "assessment": {
        "type": DataTypes.TEXT
      },
      "aspect_name": {
        "type": DataTypes.TEXT
      },
      "value_type": {
        "type": DataTypes.TEXT
      },
      "value": {
        "type": DataTypes.TEXT
      },
      "created_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "updated_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      }
    },
    "user_competencies_profilings": {
      "id": {
        "type": DataTypes.INTEGER,
        "primaryKey": true,
        "allowNull": false,
        "autoIncrement": true
      },
      "user_id": {
        "type": DataTypes.INTEGER
      },
      "profiling_date": {
        "type": DataTypes.STRING
      },
      "assessors": {
        "type": DataTypes.ARRAY(DataTypes.STRING)
      },
      "profile_as": {
        "type": DataTypes.STRING
      },
      "readiness": {
        "type": DataTypes.STRING
      },
      "metadata": {
        "type": DataTypes.JSONB
      },
      "created_at": {
        "type": DataTypes.DATEONLY,
        "allowNull": false
      },
      "updated_at": {
        "type": DataTypes.DATEONLY,
        "allowNull": false
      }
    },
    "user_job_vacancies": {
      "id": {
        "type": DataTypes.INTEGER,
        "primaryKey": true,
        "allowNull": false,
        "autoIncrement": true
      },
      "user_id": {
        "type": DataTypes.INTEGER,
        "allowNull": false
      },
      "job_vacancy_id": {
        "type": DataTypes.INTEGER,
        "allowNull": false
      },
      "competency_match": {
        "type": DataTypes.DOUBLE,
        "allowNull": false,
        "defaultValue": 0
      },
      "skill_match": {
        "type": DataTypes.DOUBLE,
        "allowNull": false,
        "defaultValue": 0
      },
      "status": {
        "type": DataTypes.STRING
      },
      "created_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "updated_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "match_rate": {
        "type": DataTypes.DOUBLE
      }
    },
    "user_job_variable_constants": {
      "id": {
        "type": DataTypes.INTEGER,
        "primaryKey": true,
        "allowNull": false,
        "autoIncrement": true
      },
      "user_job_variable_id": {
        "type": DataTypes.INTEGER,
        "allowNull": false
      },
      "constant": {
        "type": DataTypes.INTEGER,
        "allowNull": false,
        "defaultValue": 0
      },
      "match_score": {
        "type": DataTypes.DOUBLE,
        "allowNull": false,
        "defaultValue": 0
      },
      "created_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "updated_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      }
    },
    "user_job_variables": {
      "id": {
        "type": DataTypes.INTEGER,
        "primaryKey": true,
        "allowNull": false,
        "autoIncrement": true
      },
      "user_id": {
        "type": DataTypes.INTEGER,
        "allowNull": false
      },
      "job_variable_id": {
        "type": DataTypes.INTEGER,
        "allowNull": false
      },
      "created_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "updated_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "raw_value": {
        "type": DataTypes.STRING
      },
      "normalized_value": {
        "type": DataTypes.DOUBLE,
        "allowNull": false,
        "defaultValue": 0
      }
    },
    "user_performance_reviews": {
      "id": {
        "type": DataTypes.INTEGER,
        "primaryKey": true,
        "allowNull": false,
        "autoIncrement": true
      },
      "user_position_id": {
        "type": DataTypes.INTEGER
      },
      "review_type": {
        "type": DataTypes.STRING
      },
      "review_result": {
        "type": DataTypes.JSONB
      },
      "created_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "updated_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      }
    },
    "user_positions": {
      "id": {
        "type": DataTypes.INTEGER,
        "primaryKey": true,
        "allowNull": false,
        "autoIncrement": true
      },
      "user_id": {
        "type": DataTypes.INTEGER,
        "allowNull": false
      },
      "role_name": {
        "type": DataTypes.STRING,
        "allowNull": false
      },
      "department": {
        "type": DataTypes.STRING
      },
      "job_grade": {
        "type": DataTypes.STRING
      },
      "starts_at": {
        "type": DataTypes.DATEONLY
      },
      "ends_at": {
        "type": DataTypes.DATEONLY
      },
      "created_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "updated_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "company_name": {
        "type": DataTypes.STRING
      },
      "work_area": {
        "type": DataTypes.STRING
      },
      "division": {
        "type": DataTypes.STRING
      },
      "directorate": {
        "type": DataTypes.STRING
      },
      "grade_group": {
        "type": DataTypes.STRING
      },
      "last_education": {
        "type": DataTypes.STRING
      },
      "major": {
        "type": DataTypes.STRING
      },
      "sub_grade": {
        "type": DataTypes.STRING
      },
      "employee_id": {
        "type": DataTypes.STRING
      }
    },
    "user_profiles": {
      "id": {
        "type": DataTypes.INTEGER,
        "primaryKey": true,
        "allowNull": false,
        "autoIncrement": true
      },
      "user_id": {
        "type": DataTypes.INTEGER,
        "allowNull": false,
        "unique": true
      },
      "phone_number": {
        "type": DataTypes.STRING
      },
      "location": {
        "type": DataTypes.STRING
      },
      "manager": {
        "type": DataTypes.STRING
      },
      "current_position": {
        "type": DataTypes.JSONB
      },
      "years_experience": {
        "type": DataTypes.INTEGER
      },
      "performance_rating": {
        "type": DataTypes.DOUBLE
      },
      "last_promotion": {
        "type": DataTypes.DATEONLY
      },
      "education": {
        "type": DataTypes.STRING
      },
      "competencies": {
        "type": DataTypes.ARRAY(DataTypes.STRING)
      },
      "skills": {
        "type": DataTypes.ARRAY(DataTypes.STRING)
      },
      "created_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "updated_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      }
    },
    "user_vacancy_group_variables": {
      "id": {
        "type": DataTypes.INTEGER,
        "primaryKey": true,
        "allowNull": false,
        "autoIncrement": true
      },
      "user_id": {
        "type": DataTypes.INTEGER,
        "allowNull": false
      },
      "vacancy_group_variable_id": {
        "type": DataTypes.INTEGER,
        "allowNull": false
      },
      "average_match_score": {
        "type": DataTypes.DOUBLE,
        "allowNull": false,
        "defaultValue": 0
      },
      "created_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "updated_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      }
    },
    "users": {
      "id": {
        "type": DataTypes.INTEGER,
        "primaryKey": true,
        "allowNull": false,
        "autoIncrement": true
      },
      "name": {
        "type": DataTypes.STRING,
        "allowNull": false
      },
      "email": {
        "type": DataTypes.STRING,
        "allowNull": false,
        "unique": true
      },
      "password_digest": {
        "type": DataTypes.STRING,
        "allowNull": false
      },
      "role": {
        "type": DataTypes.STRING,
        "allowNull": false,
        "defaultValue": "user"
      },
      "created_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "updated_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      }
    },
    "vacancy_group_variables": {
      "id": {
        "type": DataTypes.INTEGER,
        "primaryKey": true,
        "allowNull": false,
        "autoIncrement": true
      },
      "job_vacancy_id": {
        "type": DataTypes.INTEGER,
        "allowNull": false
      },
      "job_group_variable_id": {
        "type": DataTypes.INTEGER,
        "allowNull": false
      },
      "keyword_match_count": {
        "type": DataTypes.INTEGER,
        "allowNull": false,
        "defaultValue": 0
      },
      "match_type": {
        "type": DataTypes.STRING,
        "allowNull": false,
        "defaultValue": "filter"
      },
      "weight": {
        "type": DataTypes.DOUBLE
      },
      "created_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "updated_at": {
        "type": DataTypes.DATE,
        "allowNull": false
      },
      "filters": {
        "type": DataTypes.JSONB,
        "allowNull": false,
        "defaultValue": {}
      },
      "keyword_total_count": {
        "type": DataTypes.INTEGER,
        "allowNull": false,
        "defaultValue": 0
      }
    }
  },
  "allIndexes": {
    "internal_job_profile_data": [
      {
        "name": "internal_job_profile_data_position_name_index",
        "fields": [
          "position_name"
        ]
      }
    ],
    "job_titles": [
      {
        "name": "job_titles_name_key",
        "fields": [
          "name"
        ],
        "unique": true
      },
      {
        "name": "job_titles_name_unique_constraint",
        "fields": [
          "name"
        ],
        "unique": true
      }
    ],
    "job_vacancies": [
      {
        "name": "job_vacancies_job_title_id_index",
        "fields": [
          "job_title_id"
        ]
      }
    ],
    "job_variables": [
      {
        "name": "job_variables_job_group_variable_id",
        "fields": [
          "job_group_variable_id"
        ]
      },
      {
        "name": "job_variables_name_jgv_id_unique_constraint",
        "fields": [
          "name",
          "job_group_variable_id"
        ],
        "unique": true
      }
    ],
    "user_competencies_profilings": [
      {
        "name": "user_competencies_profilings_user_id",
        "fields": [
          "user_id"
        ]
      }
    ],
    "user_job_vacancies": [
      {
        "name": "user_job_vacancies_job_vacancy_id",
        "fields": [
          "job_vacancy_id"
        ]
      },
      {
        "name": "user_job_vacancies_status",
        "fields": [
          "status"
        ]
      },
      {
        "name": "user_job_vacancies_user_id",
        "fields": [
          "user_id"
        ]
      },
      {
        "name": "user_job_vacancies_user_id_job_vacancy_id_unique_constraint",
        "fields": [
          "user_id",
          "job_vacancy_id"
        ],
        "unique": true
      }
    ],
    "user_job_variable_constants": [
      {
        "name": "user_job_variable_constants_ujv_id_constant_unique_constraint",
        "fields": [
          "user_job_variable_id",
          "constant"
        ],
        "unique": true
      },
      {
        "name": "user_job_variable_constants_user_job_variable_id",
        "fields": [
          "user_job_variable_id"
        ]
      }
    ],
    "user_job_variables": [
      {
        "name": "user_job_variables_job_variable_id",
        "fields": [
          "job_variable_id"
        ]
      },
      {
        "name": "user_job_variables_user_id",
        "fields": [
          "user_id"
        ]
      },
      {
        "name": "user_job_variables_user_id_jv_id_unique_constraint",
        "fields": [
          "user_id",
          "job_variable_id"
        ],
        "unique": true
      }
    ],
    "user_positions": [
      {
        "name": "user_positions_user_id",
        "fields": [
          "user_id"
        ]
      }
    ],
    "user_profiles": [
      {
        "name": "user_profiles_user_id",
        "fields": [
          "user_id"
        ]
      },
      {
        "name": "user_profiles_user_id_key",
        "fields": [
          "user_id"
        ],
        "unique": true
      }
    ],
    "user_vacancy_group_variables": [
      {
        "name": "user_vacancy_group_variables_user_id_vgv_id_unique_constraint",
        "fields": [
          "user_id",
          "vacancy_group_variable_id"
        ],
        "unique": true
      }
    ],
    "users": [
      {
        "name": "users_email_key",
        "fields": [
          "email"
        ],
        "unique": true
      },
      {
        "name": "users_email_unique_constraint",
        "fields": [
          "email"
        ],
        "unique": true
      }
    ],
    "vacancy_group_variables": [
      {
        "name": "vacancy_group_variables_vacancy_id_jgv_id_unique_constraint",
        "fields": [
          "job_vacancy_id",
          "job_group_variable_id"
        ],
        "unique": true
      }
    ]
  },
  "appliedMigrations": [
    "20250811230230-create-users.js",
    "20250815023405-create-job-titles.js",
    "20250815031343-create-job-vacancies.js",
    "20250815041927-create-user-profiles.js",
    "20250815041931-create-user-positions.js",
    "20250826010440-create-user-job-vacancies.js",
    "20250830011852-add-ksao-to-job-vacancies.js",
    "20250830024449-create-internal-job-profile-data.js",
    "20250830034656-change-column-type-work-output-internal-job-data.js",
    "20250830040838-create-job-group-variables.js",
    "20250830041032-create-job-variables.js",
    "20250830041427-create-vacancy-group-variables.js",
    "20250830041844-create-user-job-variables.js",
    "20250830041959-create-user-vacancy-group-variables.js",
    "20250830042048-add-match-rate-to-user-job-vacancies.js",
    "20250830045405-create-user-competencies-profiling.js",
    "20250830051125-create-user-performance-review.js",
    "20250830053632-create-user-assessment-result.js",
    "20250830074740-add-unique-constraint-user-job-vacancies.js",
    "20250830074825-change-allow-null-job-title-id-to-job-vancancies.js",
    "20250830093335-add-column-related-user-ids-and-onetsoc-codes-to-job-vacancies.js",
    "20250831013044-add-status-to-job-vacancies.js",
    "20250831013751-change-user-job-vacancies-status-column.js",
    "20250831014329-add-name-to-job-variables.js",
    "20250831014431-add-description-and-order-level-to-job-group-variables.js",
    "20250831014533-add-filters-to-vacancy-group-variables.js",
    "20250831035136-add-keyword-total-count-to-vgv.js",
    "20250831045740-change-job-vacancies-department-and-job-grades-null.js",
    "20250831051024-drop-competencies-skills-from-job-vacancies.js",
    "20250831073243-modify-some-table-fields-for-spreadsheets.js",
    "20250831085902-rename-createdat-updatedat-to-snake-case.js",
    "20250831140257-change-some-user-job-variable-columns.js",
    "20250831140512-create-user-job-variable-constants.js",
    "20250831150554-remove-unused-columns-from-job-variables.js",
    "20250831151155-change-some-job-variables-columns.js",
    "20250831152152-add-unique-job-variables-name.js",
    "20250831152200-add-unique-user-job-variables.js",
    "20250831161419-change-job-variables-unique-constraint.js",
    "20250901051252-add-more-fields-to-user-positions.js",
    "20250901070156-add-employee-id-to-user-positions.js",
    "20250902022643-add-column-metadata-to-job-vacancies.js",
    "20250902092858-change-metadata-column-name-to-detailed-desriptions-in-job-vacancies.js",
    "20250902102703-create-job-levels.js",
    "20250902102800-add-job-level-id-to-job-vacancies.js",
    "20250904024522-create-llm-metadata-table.js",
    "20250904024838-add-column-action-type-to-llm-metadata.js",
    "20250904043520-rename-timestamps-on-llm-metadata.js"
  ]
};
