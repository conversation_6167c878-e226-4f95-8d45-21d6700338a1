const AppService = require('./AppService');
const {
  VacancyGroupVariable,
  JobGroupVariable,
  JobVacancy,
  // JobVariable,
  sequelize,
} = require('../models');
const JobVacancyService = require('./JobVacancyService');

class VacancyGroupVariableService extends AppService {
  /**
   * Find all vacancy group variables for a job vacancy with pagination and sorting
   * @param {Object} queryParams - Query parameters including job_vacancy_id
   * @returns {Object} Vacancy group variables with pagination info
   */
  async findAll(queryParams) {
    const {
      job_vacancy_id,
      page = 1,
      limit = 10,
      sort = 'id',
      sort_direction = 'asc',
    } = queryParams;

    let validPage = parseInt(page);
    if (validPage < 1) validPage = 1;

    let validLimit = parseInt(limit);
    if (validLimit < 1) validLimit = 1;
    if (validLimit > 100) validLimit = 100;

    const offset = (validPage - 1) * validLimit;
    const order = [[sort, sort_direction.toUpperCase()]];

    // Build the query with associations
    const queryOptions = {
      where: { job_vacancy_id },
      include: [
        {
          model: JobGroupVariable,
          as: 'jobGroupVariable',
          attributes: ['id', 'name', 'description', 'order_level', 'keywords'],
        },
      ],
      limit: validLimit,
      offset,
      order,
    };

    const result = await VacancyGroupVariable.findAndCountAll(queryOptions);

    // Process the results to include keyword_total_count from job_group_variable keywords
    const processedRows = result.rows.map(vgv => {
      const vgvData = vgv.toJSON();
      if (vgvData.jobGroupVariable && vgvData.jobGroupVariable.keywords) {
        vgvData.keyword_total_count = vgvData.jobGroupVariable.keywords.length;
      }
      return vgvData;
    });

    const jgvsById = await Promise.all(
      result.rows.map(async vgv => {
        const jgv = await vgv.getJobGroupVariable();
        return { [jgv.id]: jgv };
      }),
    );

    return {
      vacancy_group_variables: processedRows,
      jgvsById: jgvsById.reduce((acc, jgv) => ({ ...acc, ...jgv }), {}),
      pagination: {
        page: validPage,
        limit: validLimit,
        total: result.count,
      },
    };
  }

  /**
   * Bulk update vacancy group variables
   * @param {Array} vacancyGroupVariables - Array of vacancy group variables to update
   * @param {Object} options - Options including transaction
   * @returns {Object} Update result
   */
  async bulkUpdate(vacancyGroupVariables) {
    const useTransaction = process.env.NODE_ENV !== 'test';
    const transaction = useTransaction ? await sequelize.transaction() : null;

    try {
      const jobVacancyIds = [];
      const updatePromises = vacancyGroupVariables.map(async vgvData => {
        const { id, match_type, weight, filters } = vgvData;

        // Find the existing record
        const findOptions = transaction ? { transaction } : {};
        const existingVgv = await VacancyGroupVariable.findByPk(id, findOptions);
        this.exists(existingVgv, `Vacancy group variable with id ${id} not found`);
        jobVacancyIds.push(existingVgv.job_vacancy_id);

        // Prepare update data
        const updateData = {
          match_type,
          updated_at: new Date(),
        };

        // Set weight for weight match type, null for filter match type
        if (match_type === 'weight') {
          this.assert(
            weight !== undefined && weight !== null,
            'Weight is required for weight match type',
          );
          updateData.weight = weight;
          updateData.filters = {}; // Clear filters for weight type
        } else if (match_type === 'filter') {
          this.assert(filters && filters.length > 0, 'Filters are required for filter match type');
          updateData.weight = null; // Clear weight for filter type
          updateData.filters = filters; // Store filters as JSONB
        }

        // Update the record
        const updateOptions = transaction ? { transaction } : {};
        await existingVgv.update(updateData, updateOptions);

        return existingVgv;
      });

      const updatedRecords = await Promise.all(updatePromises);

      this.assert(
        jobVacancyIds.every(id => id === jobVacancyIds[0]),
        'All vacancy group variables must belong to the same job vacancy',
      );
      const jobVacancy = await JobVacancy.findByPk(jobVacancyIds[0]);
      await jobVacancy.update({ status: 'calculating_match_scores' });

      if (useTransaction) {
        await transaction.commit();
      }

      this.calculateUserVariableConstants(jobVacancy.id);

      return {
        updated_count: updatedRecords.length,
        updated_records: updatedRecords,
      };
    } catch (error) {
      if (useTransaction) {
        await transaction.rollback();
      }
      throw error;
    }
  }

  async calculateUserVariableConstants(jobVacancyId) {
    const jobVacancyService = new JobVacancyService();
    const jobVacancy = await JobVacancy.findByPk(jobVacancyId);

    // commented out for testing new method
    // const userIds = jobVacancy.related_user_ids;

    // const vgv = await VacancyGroupVariable.findAll({
    //   where: { job_vacancy_id: jobVacancyId },
    // });

    // await Promise.all(
    //   vgv.map(async vgv => {
    //     const vars = await JobVariable.findAll({
    //       where: { job_group_variable_id: vgv.job_group_variable_id },
    //     });

    //     const constant = vgv.keyword_match_count;
    //     const baselineModifier = constant / vgv.keyword_total_count;

    //     await Promise.all(
    //       vars.map(async jobVar => {
    //         let currentConstant;
    //         let currentBaseline;

    //         if (jobVar.variable_type === 'numeric') {
    //           currentConstant = constant;
    //           currentBaseline = baselineModifier * jobVar.normalized_baseline;
    //         } else {
    //           currentConstant = 0;
    //           currentBaseline = 100;
    //         }

    //         const sql = `
    //           INSERT INTO user_job_variable_constants (
    //             user_job_variable_id,
    //             constant,
    //             match_score,
    //             created_at,
    //             updated_at
    //           )
    //           SELECT ujv.id AS user_job_variable_id, (:current_constant)::int AS constant,
    //             LEAST(
    //               CASE
    //                 WHEN (:current_baseline)::float = 0 THEN 100
    //                 ELSE (ujv.normalized_value / (:current_baseline)::float) * 100
    //               END,
    //               100
    //             ) AS match_score,
    //             NOW() AS created_at,
    //             NOW() AS updated_at
    //           FROM user_job_variables ujv
    //           LEFT JOIN user_job_variable_constants ujvc
    //             ON ujvc.user_job_variable_id = ujv.id
    //             AND ujvc.constant = (:current_constant)::int
    //           WHERE ujv.job_variable_id = :job_variable_id
    //             AND ujvc.id IS NULL
    //         `;

    //         await sequelize.query(sql, {
    //           replacements: {
    //             current_baseline: currentBaseline,
    //             job_variable_id: jobVar.id,
    //             current_constant: currentConstant,
    //           },
    //           type: sequelize.QueryTypes.RAW,
    //         });
    //       }),
    //     );
    //   }),
    // );

    await jobVacancyService.averagingVarGroup(jobVacancyId);
    await jobVacancyService.weightingMatchRate(jobVacancyId);
    await jobVacancy.update({ status: 'active' });
  }
}

module.exports = VacancyGroupVariableService;
