version: 2.1

orbs:
  aws-ecr: circleci/aws-ecr@9.3.2
  aws-cli: circleci/aws-cli@5.1.0
  aws-eks: circleci/aws-eks@2.2.0

executors:
  k8s:
    working_directory: ~/paragon-api
    docker:
      - image: cimg/node:22.16
    resource_class: large
    environment:
      NODE_ENV: staging

  jest:
    working_directory: ~/paragon-api
    docker:
      - image: cimg/node:22.16
      - image: cimg/postgres:15.3
        environment:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: paragon_api_test
    resource_class: medium
    environment:
      NODE_ENV: test

commands:
  migrate-db:
    steps:
      - run:
          name: Prepare environment files
          command: |
            echo "Creating .env file from cluster configmap..."
            touch .env
            kubectl get cm paragon-app-config -o jsonpath='{.data.\.env}' > .env
            chmod 644 .env

            echo "Creating database.json from cluster configmap..."
            touch database.json
            kubectl get cm paragon-app-config -o jsonpath='{.data.database\.json}' > database.json
            chmod 644 database.json
      - run:
          name: Verify schema migrations
          command: |
            trap 'echo "Cleaning up container..."; docker rm -f "$CONTAINER_ID"' EXIT

            echo "Setting up schema verification container..."
            CONTAINER_ID=$(docker create \
              -e NODE_ENV=staging \
              $AWS_ECR_REGISTRY_ID.dkr.ecr.$AWS_REGION.amazonaws.com/paragon-api:$IMAGE_TAG \
              npm run db:verify)

            echo "Copying environment files to container..."
            docker cp "$(pwd)/.env" "$CONTAINER_ID:/app/.env"
            docker cp "$(pwd)/database.json" "$CONTAINER_ID:/app/src/config/database.json"

            echo "Verifying schema migrations..."
            docker start -a "$CONTAINER_ID"

            docker rm "$CONTAINER_ID"
            echo "Schema verification completed successfully."
      - run:
          name: Migrate database
          command: |
            trap 'echo "Cleaning up container..."; docker rm -f "$CONTAINER_ID"' EXIT

            echo "Setting up database migration container..."
            CONTAINER_ID=$(docker create \
              -e NODE_ENV=staging \
              $AWS_ECR_REGISTRY_ID.dkr.ecr.$AWS_REGION.amazonaws.com/paragon-api:$IMAGE_TAG \
              npm run db:migrate)

            echo "Copying environment files to container..."
            docker cp "$(pwd)/.env" "$CONTAINER_ID:/app/.env"
            docker cp "$(pwd)/database.json" "$CONTAINER_ID:/app/src/config/database.json"

            echo "Running database migrations..."
            docker start -a "$CONTAINER_ID"

            docker rm "$CONTAINER_ID"            
            echo "Database migration completed successfully."

jobs:
  test:
    executor: jest
    steps:
      - checkout
      - run:
          name: Install dependencies
          command: npm ci
      - run:
          name: Install dockerize
          command: |
            wget https://github.com/jwilder/dockerize/releases/download/$DOCKERIZE_VERSION/dockerize-linux-amd64-$DOCKERIZE_VERSION.tar.gz
            sudo tar -C /usr/local/bin -xzvf dockerize-linux-amd64-$DOCKERIZE_VERSION.tar.gz
            rm dockerize-linux-amd64-$DOCKERIZE_VERSION.tar.gz
          environment:
            DOCKERIZE_VERSION: v0.6.1
      - run:
          name: Wait for PostgreSQL
          command: |
            dockerize -wait tcp://localhost:5432 -timeout 1m
      - run:
          name: Setup test env file
          command: |
            echo 'NODE_ENV=test
            PORT=3001
            HOST=localhost
            JWT_SECRET=test_jwt_secret_key_for_testing_only
            JWT_EXPIRES_IN=1h
            DB_HOST=localhost
            DB_PORT=5432
            DB_NAME=paragon_api_test
            DB_USER=postgres
            DB_PASSWORD=postgres
            SALT_ROUNDS=4
            RATE_LIMIT_WINDOW_MS=60000
            RATE_LIMIT_MAX_REQUESTS=1000
            RATE_LIMIT_AUTH_WINDOW_MS=60000
            RATE_LIMIT_AUTH_MAX_REQUESTS=50' > .env.test
      - run:
          name: Setup test env db config
          command: |
            mkdir -p src/config
            echo '{
              "test": {
                "username": "postgres",
                "password": "postgres",
                "database": "paragon_api_test",
                "host": "localhost",
                "port": 5432,
                "dialect": "postgres",
                "logging": false
              }
            }' > src/config/database.json
      - run:
          name: Run tests
          command: npm test

  deploy-staging:
    executor: k8s
    steps:
      - run:
          name: Start timer
          command: |
            echo "export START_TIME=$(date +%s)" >> $BASH_ENV
      - checkout
      - run:
          name: Set tag image
          command: |
            COMMIT_HASH=$(git rev-parse --short HEAD)
            echo "Using commit hash: $COMMIT_HASH"
            echo "export IMAGE_TAG=$COMMIT_HASH" >> $BASH_ENV
      - setup_remote_docker:
          docker_layer_caching: false
      - aws-ecr/build_and_push_image:
          repo: paragon-api
          tag: $IMAGE_TAG
          auth:
            - aws-cli/setup:
                region: $AWS_REGION
          region: $AWS_REGION
          checkout: false
          account_id: $AWS_ECR_REGISTRY_ID
      - aws-eks/update-kubeconfig-with-authenticator:
          cluster-name: $CLUSTER_NAME
          install-kubectl: true
          kubectl-version: v1.27.7
          aws-region: $AWS_REGION
      - migrate-db
      - run:
          name: Set deployment image of paragon-app
          command: |
            echo "Updating paragon-app deployment with image tag: $IMAGE_TAG"
            kubectl set image deploy/paragon-app paragon-app=$AWS_ECR_REGISTRY_ID.dkr.ecr.$AWS_REGION.amazonaws.com/paragon-api:$IMAGE_TAG
      - run:
          name: Annotate deployment with commit hash
          command: |
            echo "Annotating paragon-app deployment with commit hash: $IMAGE_TAG"
            kubectl annotate deploy/paragon-app kubernetes.io/change-cause="$CIRCLE_BRANCH:$CIRCLE_SHA1:$IMAGE_TAG"
      - run:
          name: End timer and calculate duration
          command: |
            echo "export END_TIME=$(date +%s)" >> $BASH_ENV
            echo 'export DURATION=$(printf "%02d:%02d" $(( ($END_TIME - $START_TIME) / 60 )) $(( ($END_TIME - $START_TIME) % 60 )))' >> $BASH_ENV
workflows:
  version: 2

  run-tests:
    jobs:
      - test:
          filters:
            branches:
              only: /.*/
    when:
      matches:
        pattern: "^.*\\[run tests\\].*$"
        value: << pipeline.git.commit.subject >>

  deploy-k8s-cluster:
    jobs:
      - approval:
          type: approval
          filters:
            branches: { ignore: [master, /(release|hotfix|v)\/.*/] }
      - deploy-staging:
          name: deploy staging to k8s cluster
          requires: [approval]
          filters:
            branches: { ignore: [master, /(release|hotfix|v)\/.*/] }
    when:
      not:
        matches:
          pattern: "^.*\\[run tests\\].*$"
          value: << pipeline.git.commit.subject >>